import mysql.connector
import sys
from mysql.connector import errorcode

# --- 您需要修改的配置 ---

# 数据库连接信息
DB_CONFIG = {
    'user': 'root',
    'password': 'BwYvW64msQ5Lndu2Umpn',
    # 注意：主机和端口号最好分开写，如果主机字符串里带了端口，程序会自动解析
    'host': 'nexus-master.cl4kksw821u9.ap-east-1.rds.amazonaws.com',
    'port': 3306,
    'database': 'develop_staging'
}

# 您想要监控的n张表的表名列表
TABLES_TO_MONITOR = [
    'admin_users',
    'orders',
    'order_statistics',
    # ... 在这里继续添加您关心的其他表名
]

# -------------------------

def create_db_connection():
    """建立并返回一个数据库连接"""
    try:
        # 如果主机名中包含端口，则解析它
        if ':' in DB_CONFIG['host']:
            host, port_str = DB_CONFIG['host'].split(':')
            DB_CONFIG['host'] = host
            DB_CONFIG['port'] = int(port_str)

        cnx = mysql.connector.connect(**DB_CONFIG)
        print("✅ 数据库连接成功。")
        return cnx
    except mysql.connector.Error as err:
        if err.errno == errorcode.ER_ACCESS_DENIED_ERROR:
            print("❌ 数据库连接失败：用户名或密码错误。")
        elif err.errno == errorcode.ER_BAD_DB_ERROR:
            print(f"❌ 数据库连接失败：数据库 '{DB_CONFIG['database']}' 不存在。")
        else:
            print(f"❌ 数据库连接失败：{err}")
        sys.exit(1) # 连接失败则直接退出程序

def get_primary_key(cursor, table_name):
    """自动获取指定表的主键列名"""
    try:
        cursor.execute(f"SHOW KEYS FROM `{table_name}` WHERE Key_name = 'PRIMARY'")
        keys = cursor.fetchall()
        if not keys:
            print(f"⚠️ 警告: 表 '{table_name}' 没有找到主键。将无法进行有效对比。")
            return None
        # 假设为单主键，这是最常见的情况
        pk_column = keys[0]['Column_name']
        print(f"  - 自动发现表 '{table_name}' 的主键为: '{pk_column}'")
        return pk_column
    except Exception as e:
        print(f"❌ 获取表 '{table_name}' 主键时出错: {e}")
        return None


def fetch_table_data(cursor, table_name, pk_column):
    """获取一个表的所有数据，并以主键为key存入字典"""
    data_dict = {}
    try:
        cursor.execute(f"SELECT * FROM `{table_name}`")
        for row in cursor.fetchall():
            # 将row中的所有值转换为字符串，便于后续统一比较
            str_row = {k: str(v) for k, v in row.items()}
            data_dict[str_row[pk_column]] = str_row
    except Exception as e:
        print(f"❌ 查询表 '{table_name}' 数据时出错: {e}")
        return None
    return data_dict

def compare_and_print_diff(table_name, data_before, data_after, pk_column):
    """对比单个表的数据变化并打印结果"""
    print(f"\n==================== 正在对比表: {table_name} ====================")
    
    keys_before = set(data_before.keys())
    keys_after = set(data_after.keys())

    added_keys = keys_after - keys_before
    deleted_keys = keys_before - keys_after
    common_keys = keys_before & keys_after
    
    has_changes = False

    # 1. 查找新增的行
    if added_keys:
        has_changes = True
        print(f"---【新增了 {len(added_keys)} 行】---")
        for key in added_keys:
            print(f"  主键 '{pk_column}': {key}, 新增数据: {data_after[key]}")

    # 2. 查找删除的行
    if deleted_keys:
        has_changes = True
        print(f"---【删除了 {len(deleted_keys)} 行】---")
        for key in deleted_keys:
            print(f"  主键 '{pk_column}': {key}, 被删数据: {data_before[key]}")

    # 3. 查找修改的行
    modified_rows = []
    for key in common_keys:
        row_before = data_before[key]
        row_after = data_after[key]
        if row_before != row_after:
            changes = {}
            for field in row_before:
                if row_before[field] != row_after[field]:
                    changes[field] = {
                        '旧值': row_before[field],
                        '新值': row_after[field]
                    }
            if changes:
                 modified_rows.append({'pk': key, 'changes': changes})
    
    if modified_rows:
        has_changes = True
        print(f"---【修改了 {len(modified_rows)} 行】---")
        for item in modified_rows:
            print(f"  主键 '{pk_column}': {item['pk']}")
            for field, values in item['changes'].items():
                print(f"    - 字段 '{field}': 从 '{values['旧值']}' -> 变为 '{values['新值']}'")
    
    if not has_changes:
        print("数据完全一致，未发现任何变化。")
    
    print(f"==================== 表 {table_name} 对比结束 ====================\n")


def main():
    """程序主入口"""
    # 阶段一：获取操作前的数据快照
    print("--- 第一步：正在获取操作前的数据快照... ---")
    cnx_before = create_db_connection()
    # 使用 DictCursor 可以让查询结果返回字典形式
    cursor_before = cnx_before.cursor(dictionary=True)
    
    snapshot_data = {}

    for table in TABLES_TO_MONITOR:
        pk = get_primary_key(cursor_before, table)
        if pk:
            data = fetch_table_data(cursor_before, table, pk)
            if data is not None:
                snapshot_data[table] = {'pk': pk, 'data': data}
    
    cursor_before.close()
    cnx_before.close()
    print("✅ 操作前数据快照已保存在内存中。")

    # 暂停，等待用户操作
    try:
        input("\n>>> 请现在去执行您的业务操作，完成后回到这里，按 Enter键 继续进行对比...")
    except KeyboardInterrupt:
        print("\n用户中断操作，程序退出。")
        sys.exit(0)

    # 阶段二：获取操作后的数据并进行对比
    print("\n--- 第二步：正在获取操作后的数据并进行对比... ---")
    cnx_after = create_db_connection()
    cursor_after = cnx_after.cursor(dictionary=True)

    for table in TABLES_TO_MONITOR:
        if table in snapshot_data:
            pk = snapshot_data[table]['pk']
            data_before = snapshot_data[table]['data']
            data_after = fetch_table_data(cursor_after, table, pk)
            
            if data_after is not None:
                compare_and_print_diff(table, data_before, data_after, pk)

    cursor_after.close()
    cnx_after.close()
    print("🎉 所有表对比完成。")


if __name__ == '__main__':
    main()